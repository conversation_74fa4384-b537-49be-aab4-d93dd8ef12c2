import React from 'react';
import { CheckCircle, Home, Calendar, Mail, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const BookingSuccess = () => {
  const handleGoHome = () => {
    window.location.href = 'https://chalakadulangaphotography.com';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        {/* Success Animation Container */}
        <div className="text-center mb-8">
          <div className="relative inline-block">
            <div className="absolute inset-0 bg-green-100 rounded-full animate-ping opacity-75"></div>
            <div className="relative bg-green-500 rounded-full p-6">
              <CheckCircle className="w-16 h-16 text-white" />
            </div>
          </div>
        </div>

        {/* Main Success Card */}
        <Card className="border-0 shadow-2xl bg-white">
          <CardContent className="p-8 text-center">
            {/* Header */}
            <div className="mb-6">
              <h1 className="text-3xl font-bold text-gray-800 mb-2">
                Booking Submitted Successfully!
              </h1>
              <p className="text-lg text-gray-600">
                Thank you for choosing Chalaka Dulanga Photography
              </p>
            </div>

            {/* Success Message */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div className="text-left">
                  <h3 className="font-semibold text-green-800 mb-2">
                    Your inquiry has been received
                  </h3>
                  <p className="text-green-700 text-sm leading-relaxed">
                    We've received your booking inquiry and will review all the details you provided.
                    Our team will get back to you within 24-48 hours to discuss your photography needs
                    and confirm availability for your special day.
                  </p>
                </div>
              </div>
            </div>

            {/* What's Next Section */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">What happens next?</h2>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="bg-blue-500 rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-3">
                    <Mail className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="font-medium text-gray-800 mb-1">1. Review</h3>
                  <p className="text-sm text-gray-600">We'll review your inquiry and check availability</p>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="bg-purple-500 rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-3">
                    <Phone className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="font-medium text-gray-800 mb-1">2. Contact</h3>
                  <p className="text-sm text-gray-600">We'll reach out to discuss details and pricing</p>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <div className="bg-green-500 rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-3">
                    <Calendar className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="font-medium text-gray-800 mb-1">3. Confirm</h3>
                  <p className="text-sm text-gray-600">Finalize booking with advance payment</p>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-gray-50 rounded-lg p-6 mb-8">
              <h3 className="font-semibold text-gray-800 mb-3">Need immediate assistance?</h3>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Phone className="w-4 h-4" />
                  <span className="text-sm">+94 76 324 9526</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <Mail className="w-4 h-4" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('https://wa.me/94763249526', '_blank')}
                  className="border-green-300 text-green-700 hover:bg-green-50 transition-colors duration-200"
                >
                  WhatsApp Chat
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleGoHome}
                className="bg-black hover:bg-gray-800 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <Home className="w-4 h-4" />
                <span>Back to Homepage</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => window.open('https://www.facebook.com/chalakadulangaphotography/photos_albums', '_blank')}
                className="border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-3 rounded-lg font-medium transition-colors duration-200"
              >
                View Our Portfolio
              </Button>
            </div>

            {/* Footer Note */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                This confirmation page will remain accessible for your reference.
                Please save this page or take a screenshot for your records.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Additional Info Card */}
        <Card className="mt-6 border-0 shadow-lg bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-6 text-center">
            <h3 className="font-semibold text-gray-800 mb-2">Follow us for inspiration</h3>
            <p className="text-sm text-gray-600 mb-4">
              Stay updated with our latest work and photography tips
            </p>
            <div className="flex justify-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open('https://www.instagram.com/chalaka_dulanga_photography_/', '_blank')}
                className="text-gray-600 hover:text-gray-800"
              >
                Instagram
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open('https://www.facebook.com/chalakadulangaphotography', '_blank')}
                className="text-gray-600 hover:text-gray-800"
              >
                Facebook
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open('https://wa.me/94763249526', '_blank')}
                className="text-gray-600 hover:text-gray-800"
              >
                WhatsApp
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default BookingSuccess;
